@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 224 71.4% 4.1%;
        --card: 0 0% 100%;
        --card-foreground: 224 71.4% 4.1%;
        --popover: 0 0% 100%;
        --popover-foreground: 224 71.4% 4.1%;
        --primary: 262.1 83.3% 57.8%;
        --primary-foreground: 210 20% 98%;
        --secondary: 220 14.3% 95.9%;
        --secondary-foreground: 220.9 39.3% 11%;
        --muted: 220 14.3% 95.9%;
        --muted-foreground: 220 8.9% 46.1%;
        --accent: 220 14.3% 95.9%;
        --accent-foreground: 220.9 39.3% 11%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 20% 98%;
        --border: 220 13% 91%;
        --input: 220 13% 91%;
        --ring: 262.1 83.3% 57.8%;
        --radius: 0.5rem;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        --background: 224 71.4% 4.1%;
        --foreground: 210 20% 98%;
        --card: 224 71.4% 4.1%;
        --card-foreground: 210 20% 98%;
        --popover: 224 71.4% 4.1%;
        --popover-foreground: 210 20% 98%;
        --primary: 263.4 70% 50.4%;
        --primary-foreground: 210 20% 98%;
        --secondary: 215 27.9% 16.9%;
        --secondary-foreground: 210 20% 98%;
        --muted: 215 27.9% 16.9%;
        --muted-foreground: 217.9 10.6% 64.9%;
        --accent: 215 27.9% 16.9%;
        --accent-foreground: 210 20% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 20% 98%;
        --border: 215 27.9% 16.9%;
        --input: 215 27.9% 16.9%;
        --ring: 263.4 70% 50.4%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

.fi-sidebar {
    @apply p-2;
}
.fi-sidebar-header {
    @apply bg-none bg-transparent ring-0 shadow-none h-12 px-4;
}
.fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-header {
    @apply p-0;
}
.fi-sidebar:not(.fi-sidebar-open) .fi-global-search {
    @apply !hidden;
}
.fi-sidebar-header .fi-icon-btn {
    @apply !hidden;
}
.fi-logo {
    @apply font-semibold tracking-normal;
}
/* .fi-sidebar:not(.fi-sidebar-open) .fi-user-menu .fi-dropdown-trigger > button {
    @apply p-0 h-10;
} */
.fi-sidebar:not(.fi-sidebar-open)
    .fi-user-menu
    .fi-dropdown-trigger
    > button
    > .grid,
.fi-sidebar:not(.fi-sidebar-open)
    .fi-user-menu
    .fi-dropdown-trigger
    > button
    > svg {
    @apply !hidden;
}

.fi-sidebar-nav {
    @apply p-2 gap-y-4;
}
.fi-sidebar-nav-groups {
    @apply gap-y-4 mx-0;
}
.fi-sidebar.fi-sidebar-open .fi-sidebar-nav-tenant-menu-ctn {
    @apply !mx-0;
}

.fi-sidebar-group {
    @apply gap-0;
}
.fi-sidebar-item-button,
.fi-sidebar-group-button {
    @apply py-0 h-8 gap-x-2;
}
.fi-sidebar-group-button .fi-icon-btn {
    @apply !hidden;
}
.fi-sidebar-item-icon {
    @apply size-4 text-inherit;
}
.fi-sidebar-item-label {
    @apply font-normal;
}
.fi-sidebar-item.fi-active .fi-sidebar-item-icon {
    @apply text-primary-600 dark:text-primary-400;
}
.fi-sidebar-group-label {
    @apply text-xs;
}

.fi-main-ctn {
    @apply bg-background md:shadow md:rounded-xl md:m-2 md:ml-0;
}

.fi-topbar {
    @apply overflow-x-visible md:static border-b bg-background md:bg-transparent;
}
.fi-topbar > nav {
    @apply shadow-none ring-0 bg-none bg-transparent h-12 lg:px-6;
}
.fi-topbar .fi-user-menu {
    @apply !hidden;
}
.fi-topbar-open-sidebar-btn,
.fi-topbar-close-sidebar-btn {
    @apply !hidden;
}
.fi-topbar .fi-global-search {
    @apply !hidden;
}

.fi-user-menu .fi-avatar {
    @apply rounded-lg;
}

.fi-theme-switcher-btn > svg {
    @apply size-4;
}

.fi-main {
    @apply lg:px-6;
}

.fi-page > section {
    @apply py-6 gap-y-6;
}

.fi-breadcrumbs-list,
.fi-breadcrumbs-item {
    @apply gap-x-1;
}
.fi-breadcrumbs-item-label {
    @apply text-xs;
}
.fi-breadcrumbs-item-separator {
    @apply size-4;
}

/* .fi-header-heading {
    @apply text-lg font-medium sm:text-xl tracking-normal;
} */

.fi-tabs {
    @apply bg-muted p-1 shadow-none ring-0 rounded-lg;
}
.fi-tabs-item {
    @apply py-1 rounded-md;
}
.fi-tabs-item.fi-tabs-item-active {
    @apply shadow bg-background;
}
.fi-tabs-item-icon {
    @apply size-4;
}

.fi-ta-ctn {
    @apply rounded-none shadow-none ring-0 overflow-visible space-y-4 divide-y-0;
}
.fi-ta-ctn .fi-pagination {
    @apply px-0 py-0;
}
.fi-ta-header {
    @apply p-0;
}
.fi-ta-header-toolbar {
    @apply px-0 py-0;
}
/* .fi-ta-col-toggle .fi-dropdown-panel > .grid {
    @apply p-4;
} */
.fi-ta-col-toggle .fi-dropdown-panel > .grid > .grid {
    @apply gap-4;
}
.fi-ta-content {
    @apply !border rounded-lg;
}
.fi-ta-table > thead > tr {
    @apply bg-muted;
}
.fi-ta-selection-cell > div {
    @apply py-0;
}
.fi-ta-header-cell {
    @apply py-0 h-10;
}
.fi-ta-header-cell-label {
    @apply font-medium text-muted-foreground;
}
.fi-ta-cell .fi-ta-text {
    @apply py-3;
}
.fi-ta-icon .fi-ta-icon-item {
    @apply size-4;
}
.fi-ta-actions-cell > div {
    @apply py-0;
}

.fi-dropdown-trigger .fi-icon-btn-icon {
    @apply size-4;
}
.fi-dropdown-panel {
    @apply shadow-md border ring-0;
}
.fi-dropdown-list-item {
    @apply py-1.5;
}
.fi-dropdown-list-item-icon {
    @apply size-4;
}
.fi-dropdown-list-item-color-gray .fi-dropdown-list-item-icon {
    @apply text-popover-foreground;
}

.fi-btn {
    @apply rounded-md shadow font-medium;
}
.fi-btn-icon {
    @apply size-4;
}
.fi-icon-btn.fi-color-gray {
    @apply text-foreground hover:bg-accent hover:text-accent-foreground;
}
.fi-icon-btn-icon {
    @apply size-4;
}

.fi-input-wrp {
    @apply rounded-md;
}
.fi-input-wrp-icon {
    @apply size-4;
}
.fi-input {
    @apply py-0 h-9;
}
.fi-global-search-results-ctn {
    @apply sm:end-auto max-w-full;
}
.fi-global-search-result-link {
    @apply py-3;
}
.fi-global-search-field {
    @apply max-w-64;
}
.fi-global-search-field .fi-input {
    @apply py-0 h-8;
}
