<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Hotel extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'city',
        'country',

        'contact_email',
        'contact_phone',
    ];

    public function roomTypes(): HasMany
    {
        return $this->hasMany(RoomType::class);
    }

    public function rooms(): HasManyThrough
    {
        return $this->hasManyThrough(Room::class, RoomType::class);
    }

    public function roomAllotments(): HasMany
    {
        return $this->hasMany(RoomAllotment::class);
    }

    public function roomBasePrices(): HasMany
    {
        return $this->hasMany(RoomBasePrice::class);
    }

    public function roomSellingPrices(): HasMany
    {
        return $this->hasMany(RoomSellingPrice::class);
    }
}
