<?php

namespace App\Filament\Hotel\Resources\RoomAllotmentResource\Pages;

use App\Filament\Hotel\Resources\RoomAllotmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageRoomAllotments extends ManageRecords
{
    protected static string $resource = RoomAllotmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
