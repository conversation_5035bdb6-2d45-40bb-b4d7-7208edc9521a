<?php

namespace App\Filament\Hotel\Resources;

use App\Enums\MarkupType;
use App\Filament\Hotel\Resources\RoomSellingPriceResource\Pages;
use App\Models\RoomSellingPrice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class RoomSellingPriceResource extends Resource
{
    protected static ?string $model = RoomSellingPrice::class;

    protected static ?string $modelLabel = 'selling price';

    protected static ?string $navigationIcon = 'lucide-coins';

    protected static ?string $navigationGroup = 'Prices';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->required(),
                Forms\Components\ToggleButtons::make('customer_type')
                    ->required()
                    ->options(['b2b' => 'B2B', 'b2c' => 'B2C'])
                    ->grouped(),
                Forms\Components\Select::make('partner_id')
                    ->relationship('partner', 'company_name')
                    ->default(null)
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('season')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('start_date'),
                Forms\Components\DatePicker::make('end_date'),
                Forms\Components\TextInput::make('selling_price')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('currency_id')
                    ->relationship('currency', 'code')
                    ->default(null),
                Forms\Components\ToggleButtons::make('markup_type')
                    ->options(MarkupType::class)
                    ->required()
                    ->grouped(),
                Forms\Components\TextInput::make('markup_value')
                    ->numeric()
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('roomType.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('customer_type')
                    ->formatStateUsing(fn ($state) => strtoupper($state)),
                Tables\Columns\TextColumn::make('partner.company_name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('season')
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('selling_price')
                    ->currency(fn ($record) => $record->currency?->code ?? 'IDR', true)
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('markup_type'),
                Tables\Columns\TextColumn::make('markup_value')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roomTypes')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->searchable()
                    ->preload()
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRoomSellingPrices::route('/'),
        ];
    }
}
