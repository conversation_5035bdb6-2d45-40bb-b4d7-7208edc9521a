<?php

namespace App\Filament\Hotel\Resources;

use App\Enums\RoomStatus;
use App\Filament\Hotel\Resources\RoomResource\Pages;
use App\Models\Room;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class RoomResource extends Resource
{
    protected static ?string $model = Room::class;

    protected static ?string $navigationIcon = 'lucide-door-open';

    protected static ?string $navigationLabel = 'All Rooms';

    protected static ?string $navigationGroup = 'Rooms';

    protected static ?int $navigationSort = 0;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->required(),
                Forms\Components\TextInput::make('room_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\ToggleButtons::make('status')
                    ->inline()
                    ->columnSpanFull()
                    ->required()
                    ->default('available')
                    ->options(RoomStatus::class),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\Layout\Split::make([
                        Tables\Columns\TextColumn::make('room_number')
                            ->sortable()
                            ->searchable()
                            ->weight('bold')
                            ->size('lg'),
                        Tables\Columns\TextColumn::make('status')
                            ->badge()
                            ->alignEnd()
                            ->searchable(),
                    ]),
                    Tables\Columns\TextColumn::make('roomType.name')
                        ->sortable()
                        ->color('gray')
                        ->size('sm'),
                ]),
            ])
            ->contentGrid([
                'md' => 2,
                'xl' => 3,
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roomTypes')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->searchable()
                    ->preload()
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('room_number');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRooms::route('/'),
        ];
    }
}
