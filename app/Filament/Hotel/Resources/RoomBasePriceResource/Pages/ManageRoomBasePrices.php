<?php

namespace App\Filament\Hotel\Resources\RoomBasePriceResource\Pages;

use App\Filament\Hotel\Resources\RoomBasePriceResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageRoomBasePrices extends ManageRecords
{
    protected static string $resource = RoomBasePriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
