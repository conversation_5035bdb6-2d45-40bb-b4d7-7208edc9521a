<?php

namespace App\Filament\Hotel\Resources;

use App\Filament\Hotel\Resources\RoomAllotmentResource\Pages;
use App\Models\RoomAllotment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class RoomAllotmentResource extends Resource
{
    protected static ?string $model = RoomAllotment::class;

    protected static ?string $navigationIcon = 'lucide-grid-2x2-check';

    protected static ?string $navigationGroup = 'Rooms';

    protected static ?int $navigationSort = 30;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->required(),
                Forms\Components\Select::make('partner_id')
                    ->relationship('partner', 'company_name')
                    ->default(null),
                Forms\Components\DatePicker::make('date')
                    ->required(),
                Forms\Components\TextInput::make('total_rooms')
                    ->required()
                    ->numeric()
                    ->default(1),
                Forms\Components\TextInput::make('blocked_rooms')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('used_rooms')
                    ->required()
                    ->numeric()
                    ->disabled()
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('roomType.name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('partner.company_name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_rooms')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('blocked_rooms')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('used_rooms')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roomTypes')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->searchable()
                    ->preload()
                    ->multiple(),
                Tables\Filters\SelectFilter::make('partners')
                    ->relationship('partner', 'company_name')
                    ->searchable()
                    ->preload()
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRoomAllotments::route('/'),
        ];
    }
}
