<?php

namespace App\Filament\Hotel\Resources\RoomSellingPriceResource\Pages;

use App\Filament\Hotel\Resources\RoomSellingPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageRoomSellingPrices extends ManageRecords
{
    protected static string $resource = RoomSellingPriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
