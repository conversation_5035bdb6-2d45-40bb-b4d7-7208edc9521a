<?php

namespace App\Filament\Hotel\Resources;

use App\Filament\Hotel\Resources\RoomBasePriceResource\Pages;
use App\Models\RoomBasePrice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class RoomBasePriceResource extends Resource
{
    protected static ?string $model = RoomBasePrice::class;

    protected static ?string $modelLabel = 'base price';

    protected static ?string $navigationIcon = 'lucide-coins';

    protected static ?string $navigationGroup = 'Prices';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('room_type_id')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->required(),
                Forms\Components\TextInput::make('season')
                    ->maxLength(255)
                    ->default(null),
                Forms\Components\DatePicker::make('start_date'),
                Forms\Components\DatePicker::make('end_date'),
                Forms\Components\TextInput::make('base_price')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('currency_id')
                    ->relationship('currency', 'code')
                    ->default(null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('roomType.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('season')
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_date')
                    ->date()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('end_date')
                    ->date()
                    ->sortable()
                    ->alignEnd(),
                Tables\Columns\TextColumn::make('base_price')
                    ->currency(fn ($record) => $record->currency?->code ?? 'IDR', true)
                    ->alignRight()
                    ->sortable(),
                Tables\Columns\TextColumn::make('source')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roomTypes')
                    ->relationship('roomType', 'name', fn ($query) => $query->where('hotel_id', filament()->getTenant()->id))
                    ->searchable()
                    ->preload()
                    ->multiple(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRoomBasePrices::route('/'),
        ];
    }
}
