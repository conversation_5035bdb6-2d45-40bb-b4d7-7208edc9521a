<?php

namespace App\Filament\Hotel\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class HotelRoomsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Rooms', '0')
                ->icon('lucide-bed'),
            Stat::make('Available Rooms', '0')
                ->icon('lucide-door-open'),
            Stat::make('Occupancy Rate', '0%')
                ->icon('lucide-chart-pie'),
        ];
    }
}
