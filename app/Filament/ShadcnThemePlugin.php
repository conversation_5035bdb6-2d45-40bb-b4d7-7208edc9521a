<?php

namespace App\Filament;

use Filament\Actions;
use Filament\Contracts\Plugin;
use Filament\Forms;
use Filament\Panel;
use Filament\Tables;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Blade;

class ShadcnThemePlugin implements Plugin
{
    protected bool $useLucideIcons = false;

    protected bool $isSearchInSidebar = false;

    public function lucideIcons(bool $condition = true): static
    {
        $this->useLucideIcons = $condition;

        return $this;
    }

    public function useLucideIcons(): bool
    {
        return $this->useLucideIcons;
    }

    public function searchInSidebar(bool $condition = true): static
    {
        $this->isSearchInSidebar = $condition;

        return $this;
    }

    public function isSearchInSidebar(): bool
    {
        return $this->isSearchInSidebar;
    }

    public static function make(): static
    {
        return new static;
    }

    public function getId(): string
    {
        return 'shadcn-theme';
    }

    public function register(Panel $panel): void
    {
        $panel
            // ->viteTheme('vendor/fauzara/filament-shadcn-theme/resources/css/theme.css')
            ->sidebarWidth('16rem')
            ->renderHook(
                PanelsRenderHook::SIDEBAR_FOOTER,
                fn () => view('filament.shadcn.sidebar.user-menu')
            )
            ->renderHook(
                PanelsRenderHook::TOPBAR_START,
                fn () => view('filament.shadcn.topbar.start')
            )
            ->when($this->useLucideIcons, function (Panel $panel) {
                $panel->icons([
                    'panels::global-search.field' => 'lucide-search',
                    'panels::user-menu.profile-item' => 'lucide-circle-user-round',
                    'panels::user-menu.logout-button' => 'lucide-log-out',
                    'panels::pages.dashboard.navigation-item' => 'lucide-layout-dashboard',
                    'panels::topbar.open-database-notifications-button' => 'lucide-bell',
                    'actions::action-group' => 'lucide-ellipsis-vertical',
                    'actions::view-action' => 'lucide-eye',
                    'actions::edit-action' => 'lucide-square-pen',
                    'actions::delete-action' => 'lucide-trash-2',
                    'tables::search-field' => 'lucide-search',
                    'tables::actions.filter' => 'lucide-list-filter',
                    'tables::actions.toggle-columns' => 'lucide-columns-3',
                    'tables::columns.icon-column.true' => 'lucide-circle-check',
                    'tables::columns.icon-column.false' => 'lucide-circle-x',
                ]);
            })
            ->when($this->isSearchInSidebar, function (Panel $panel) {
                $panel->renderHook(
                    PanelsRenderHook::SIDEBAR_NAV_START,
                    fn () => Blade::render('
                        @if (filament()->isGlobalSearchEnabled())
                            @livewire(Filament\Livewire\GlobalSearch::class)
                        @endif
                        ')
                );
            });
    }

    public function boot(Panel $panel): void
    {
        Forms\Components\Section::configureUsing(function ($section) {
            $section->compact();
        });

        if ($this->useLucideIcons) {
            Actions\CreateAction::configureUsing(function ($action) {
                $action->icon('lucide-plus');
            });

            Tables\Actions\CreateAction::configureUsing(function ($action) {
                $action->icon('lucide-plus');
            });
        }

        Tables\Actions\ActionGroup::configureUsing(function ($action) {
            $action
                ->color('gray')
                ->size('sm');
        });
    }
}
