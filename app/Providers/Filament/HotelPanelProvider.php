<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Tenancy\AddHotel;
use App\Filament\Pages\Tenancy\EditHotel;
use App\Filament\ShadcnThemePlugin;
use App\Models\Hotel;
use Filament\Enums\ThemeMode;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class HotelPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('hotel')
            ->path('hotel')
            ->colors([
                'primary' => Color::hex('#D653C2'),
                'secondary' => Color::hex('#4B3EF0'),
            ])
            ->defaultThemeMode(ThemeMode::Light)
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->tenant(Hotel::class)
            ->tenantRegistration(AddHotel::class)
            ->tenantProfile(EditHotel::class)
            ->navigationGroups(['Rooms', 'Prices', 'Reservations'])
            ->discoverResources(in: app_path('Filament/Hotel/Resources'), for: 'App\\Filament\\Hotel\\Resources')
            ->discoverPages(in: app_path('Filament/Hotel/Pages'), for: 'App\\Filament\\Hotel\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Hotel/Widgets'), for: 'App\\Filament\\Hotel\\Widgets')
            ->widgets([])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->renderHook(
                PanelsRenderHook::SIDEBAR_NAV_END,
                fn () => Blade::render('
                <div class="mt-auto">
                    <x-filament-panels::sidebar.item
                        icon="lucide-arrow-left"
                        url="/admin"
                    >
                        Back to Admin panel
                    </x-filament-panels::sidebar.item>
                </div>
                ')
            )
            ->databaseNotifications()
            ->plugins([
                ShadcnThemePlugin::make()
                    ->lucideIcons()
                    ->searchInSidebar(),
            ]);
    }
}
