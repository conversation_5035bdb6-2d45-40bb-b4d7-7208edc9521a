<?php

namespace App\Providers;

use Filament\Panel;
use Filament\Support\Enums\Platform;
use Filament\Tables;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        Panel::configureUsing(function (Panel $panel) {
            $panel
                ->sidebarCollapsibleOnDesktop()
                ->globalSearchKeyBindings(['command+k', 'ctrl+k'])
                ->globalSearchFieldSuffix(fn (): ?string => match (Platform::detect()) {
                    Platform::Windows, Platform::Linux => 'CTRL+K',
                    Platform::Mac => '⌘K',
                    default => null,
                });
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Tables\Table::configureUsing(function (Tables\Table $table) {
            $table
                ->defaultPaginationPageOption(25);
        });
    }
}
